module "route-53-record" {
  source                       = "git::ssh://*****************************/terraform-modules/cross_account_hosted_zone_records.git"
  cross_account_provider_alias = "networking_assume_role"
  service_account_profile      = "${local.r53_service_account_profile}"
  dns_publisher_role_arn       = "${local.r53_dns_publisher_role_arn}"
  aws_region                   = "${var.region}"
  cross_account_hosted_zone_id = "${local.r53_hosted_zone_id}"
  record_name                  = "authn-${terraform.workspace}"
  record_type                  = "CNAME"
  record_ttl                   = "300"
  dns_name                     = "PLACEHOLDER"
}
