# main.tf
# The main entry point into the Terraform code
# Used to initialize Terraform provide, remote data and call modules
# Try to not include code here, create a ./module

terraform {
  required_version = "~> 0.11.14"
}

## AWS Provider ##
provider "aws" {
  region  = "${var.region}"
  version = "v2.70.0"
}

# recommended by terraform build
provider "null" {
  version = "~> 2.1"
}

# recommended by terraform build
provider "template" {
  version = "~> 2.1"
}

data "terraform_remote_state" "common" {
  backend   = "s3"
  workspace = "${terraform.workspace}"

  config {
    key     = "common-terraform.tfstate"
    region  = "${var.region}"
    encrypt = true
    bucket  = "${local.remote_state_bucket}"
  }
}

data "terraform_remote_state" "servicemgr" {
  backend   = "s3"
  workspace = "${terraform.workspace}"

  config {
    key     = "servicemgr-terraform.tfstate"
    region  = "${var.region}"
    encrypt = true
    bucket  = "${local.remote_state_bucket}"
  }
}
