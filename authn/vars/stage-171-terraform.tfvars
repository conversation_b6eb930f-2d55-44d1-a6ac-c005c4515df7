agora_vpc_cidr_block = "10.170.0.0/16"

ami_name_regex = "veracode-awslinux-authn-20.1.4"
ami_owners = "************"
asg_cpu_high = 70
asg_cpu_low = 30
asg_desired = 3
asg_max = 3
asg_min = 3
elb_account_id = "************"
health_check_type = "EC2"
region = "us-east-1"
remote_state_bucket = "veracode-platform-nonprod-terraform-state"

ssm_authn_kms_aws_kpk_master_key_alias = "arn:aws:kms:us-east-1:************:alias/veracode-nonproduction_vosp-master-key"
ssm_authn_kms_aws_region = "us-east-1"
ssm_authn_keystore_password = "changeit"
ssm_authn_truststore_password = "changeit"
ssm_authn_db_user = "appstg171"
ssm_authn_db_password = "VCnew_stg171"
ssm_authn_db_url = "************************************************************************************************************************************************* = stg71.ec2.internal)))"
ssm_authn_principalsigningpublickey =  "veracode.io"
ssm_authn_ingriannae_keystore_password = "changeit"
ssm_authn_ingriannae_kmsipaddress = "*************:*************"
ssm_app_kms_properties_aws_kpk_master_alias = "arn:aws:kms:us-east-1:************:key/1389d78d-7e32-41f4-8b38-381336cf0b5e"
ssm_app_kms_properties_aws_access_key = "********************"
ssm_app_kms_properties_aws_secret_access_key = "nqqtWGaBWXQkGQCrO0dznq52T7PoPHCC8IZGNWcO"
ssm_app_kms_properties_aws_region = "us-east-1"
ssm_app_kms_properties_kpk_master_alias =  "MasterKeyGarnetg"
ssm_naeserverip = "*************:*************"

tags_application = "authn"
tags_application_long = "authn"
tags_cost_center = "Analytics"
tags_domain = "stage.veracode.io"
tags_email = "<EMAIL>"
tags_env = "stage-171"
tags_owner = "Analytics"
tags_product = "platform"
tags_purpose = "AWS-TEST-STACK"
tags_stack_name = "stage-171"
tags_terraformManaged = "true"
