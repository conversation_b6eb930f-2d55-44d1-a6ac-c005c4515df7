# variable: agora_vpc_cidr_block
#   Description: "The CIDR block of the agora VPC to add to the security groups allow from"
#   default value: agora_vpc_cidr_block = "**********/16"

# variable: ami_name_regex
#   Description: "Regex to be used to search for AMI"
ami_name_regex = "veracode-awslinux-authn-21.3.0"

# variable: ami_owners
#   Description: "AWS account number of AMI owner"
#   default value: ami_owners = "************"

# variable: asg_cpu_high
#   Description: "if CPU Util is over this for over 1 min, number of instances will be increased by 1"
#   default value: asg_cpu_high = "70"

# variable: asg_cpu_low
#   Description: "if CPU Util is under this for over 1 min, number of instances will be reduced by 1"
#   default value: asg_cpu_low = "30"

# variable: asg_desired
#   Description: "Desired number of ec2 instancse for the autoscaling group"
#   default value: asg_desired = 1

# variable: asg_max
#   Description: "Maximum number of ec2 instances allowed for the autoscaling group."
#   default value: asg_max = 1

# variable: asg_min
#   Description: "Minimum number of ec2 instances allowed for the autoscaling group."
#   default value: asg_min = 1

# variable: authn_instance_type
#   default value: authn_instance_type = "t3.small"
authn_instance_type = "t3.medium"

# variable: codedeploy_trigger_events
#   Description: "The codedeploy events to trigger an SNS notification"
#   default value: codedeploy_trigger_events = [
#    "DeploymentSuccess",
#    "DeploymentFailure"
#]

# variable: codedeploy_trigger_name
#   Description: "The name of the codedeploy trigger for sending SNS notifications"
#   default value: codedeploy_trigger_name = "authn-slack-notifications"

# variable: health_check_type
#   Description: "EC2 or ELB. Controls how health checking is done."
#   default value: health_check_type = "EC2"
health_check_type = "ELB"

# variable: newrelic_enabled
#   default value: newrelic_enabled = "false"

# variable: region
#   Description: "AWS region infrastructure lives in"
#   default value: region = "us-east-1"

# variable: root_volume_size
#   default value: root_volume_size = "20"

# variable: ssm_app_kms_properties_aws_access_key
#   Description: "ssm_app_kms_properties_aws_access_key"
ssm_app_kms_properties_aws_access_key = "********************"

# variable: ssm_app_kms_properties_aws_kpk_master_alias
#   Description: "ssm_app_kms_properties_aws_kpk_master_alias"
ssm_app_kms_properties_aws_kpk_master_alias = "arn:aws:kms:us-east-1:227890167531:key/1389d78d-7e32-41f4-8b38-381336cf0b5e"

# variable: ssm_app_kms_properties_aws_region
#   Description: "ssm_app_kms_properties_aws_region"
ssm_app_kms_properties_aws_region = "us-east-1"

# variable: ssm_app_kms_properties_aws_secret_access_key
#   Description: "ssm_app_kms_properties_aws_secret_access_key"
ssm_app_kms_properties_aws_secret_access_key = "nqqtWGaBWXQkGQCrO0dznq52T7PoPHCC8IZGNWcO"

# variable: ssm_app_kms_properties_kpk_master_alias
#   Description: "ssm_app_kms_properties_kpk_master_alias"
#   Defined in common.
#   default value: ssm_app_kms_properties_kpk_master_alias = "MasterKeyGarnetg"
ssm_app_kms_properties_kpk_master_alias = "arn:aws:kms:us-east-1:227890167531:key/1389d78d-7e32-41f4-8b38-381336cf0b5e"

# variable: ssm_authn_agora_security_principal_jwt_public_file
#   Description: "ssm_authn_agora_security_principal_jwt_public_file"
#   default value: ssm_authn_agora_security_principal_jwt_public_file = "conf/agora-identityservice-principal-signing-qa-1.pem"

# variable: ssm_authn_client_auth
#   Description: "ssm_authn_client_auth"
#   default value: ssm_authn_client_auth = "want"

# variable: ssm_authn_clients_allowed
#   Description: "ssm_authn_clients_allowed"
#   default value: ssm_authn_clients_allowed = "agora-identityservice,f5-service,vosp-servicemgr"

# variable: ssm_authn_code_deploy_appname
#   Description: "ssm_authn_code_deploy_appname"
#   default value: ssm_authn_code_deploy_appname = "vosp-authn"

# variable: ssm_authn_code_deploy_grpname
#   Description: "ssm_authn_code_deploy_grpname"
#   default value: ssm_authn_code_deploy_grpname = "vosp-authn"

# variable: ssm_authn_db_init_size
#   Description: "ssm_authn_db_init_size"
#   default value: ssm_authn_db_init_size = "10"

# variable: ssm_authn_db_max_active
#   default value: ssm_authn_db_max_active = "50"

# variable: ssm_authn_db_max_idle
#   default value: ssm_authn_db_max_idle = "20"

# variable: ssm_authn_db_min_idle
#   Description: "ssm_authn_db_min_idle"
#   default value: ssm_authn_db_min_idle = "5"

# variable: ssm_authn_ec2_grp
#   Description: "ssm_authn_ec2_grp"
#   default value: ssm_authn_ec2_grp = "platform"

# variable: ssm_authn_ingriannae_keystore_file
#   Description: "ssm_authn_ingriannae_keystore_file"
#   default value: ssm_authn_ingriannae_keystore_file = "conf/truststore.kms.jks"

# variable: ssm_authn_ingriannae_keystore_password
#   Description: "ssm_authn_ingriannae_keystore_password"
ssm_authn_ingriannae_keystore_password = "changeit"

# variable: ssm_authn_ingriannae_kmsipaddress
#   Description: "ssm_authn_ingriannae_kmsipaddress"
ssm_authn_ingriannae_kmsipaddress = "*************:*************"

# variable: ssm_authn_key_file
#   Description: "ssm_authn_key_file"
#   default value: ssm_authn_key_file = "file:conf/keyfile"

# variable: ssm_authn_keystore_file
#   Description: "ssm_authn_keystore_file"
#   default value: ssm_authn_keystore_file = "file:conf/vosp-authn-keystore.jks"

# variable: ssm_authn_keystore_password
#   Description: "ssm_authn_keystore_password"
ssm_authn_keystore_password = "changeit"

# variable: ssm_authn_kms_aws_kpk_master_key_alias
#   Description: "ssm_authn_kms_aws_kpk_master_key_alias"
ssm_authn_kms_aws_kpk_master_key_alias = "arn:aws:kms:us-east-1:227890167531:alias/veracode-nonproduction_vosp-master-key"

# variable: ssm_authn_kms_aws_region
#   Description: "ssm_authn_kms_aws_region"
ssm_authn_kms_aws_region = "us-east-1"

# variable: ssm_authn_kms_aws_s3_role_arn
#   Description: "ssm_authn_kms_aws_s3_role_arn"
#   default value: ssm_authn_kms_aws_s3_role_arn = ""

# variable: ssm_authn_kms_default_kms
#   Description: "ssm_authn_kms_default_kms"
#   default value: ssm_authn_kms_default_kms = "AWSKMS"

# variable: ssm_authn_port
#   Description: "ssm_authn_port"
#   default value: ssm_authn_port = "8443"

# variable: ssm_authn_principalsigningpublickey
#   Description: "ssm_authn_principalsigningpublickey"
ssm_authn_principalsigningpublickey = "veracode.io"

# variable: ssm_authn_project_tag
#   Description: "ssm_authn_project_tag"
#   default value: ssm_authn_project_tag = "vosp-authn-deploy"

# variable: ssm_authn_redis_enabled
#   default value: ssm_authn_redis_enabled = "false"
ssm_authn_redis_enabled = "true"

# variable: ssm_authn_redis_host
#   default value: ssm_authn_redis_host = "localhost"
ssm_authn_redis_host = "agorstag142-identity.6vvrh2.0001.use1.cache.amazonaws.com"

# variable: ssm_authn_rsa_properties
#   Description: "ssm_authn_rsa_properties"
#   default value: ssm_authn_rsa_properties = "conf/rsa_api.properties"

# variable: ssm_authn_rsa_test_mode
#   Description: "ssm_authn_rsa_test_mode"
#   default value: ssm_authn_rsa_test_mode = "true"

# variable: ssm_authn_rsa_test_value
#   Description: "ssm_authn_rsa_test_value"
#   default value: ssm_authn_rsa_test_value = "555555"

# variable: ssm_authn_s3_bucket_prefix
#   Description: "ssm_authn_s3_bucket_prefix"
#   default value: ssm_authn_s3_bucket_prefix = "authn"

# variable: ssm_authn_sdconf_loc
#   Description: "ssm_authn_sdconf_loc"
#   default value: ssm_authn_sdconf_loc = "conf/sdconf.rec"

# variable: ssm_authn_server_ssl_ciphers
#   Description: "ssm_authn_server_ssl_ciphers"
#   default value: ssm_authn_server_ssl_ciphers = "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384"

# variable: ssm_authn_service_log_file
#   Description: "ssm_authn_service_log_file"
#   default value: ssm_authn_service_log_file = "logs/authn.log"

# variable: ssm_authn_source_s3_object_key
#   Description: "ssm_authn_source_s3_object_key"
#   default value: ssm_authn_source_s3_object_key = "authn/authn_deploy.zip"

# variable: ssm_authn_start_clientcertfile
#   Description: "ssm_authn_start_clientcertfile"
#   default value: ssm_authn_start_clientcertfile = "conf/vosp-authn-qa.pem"

# variable: ssm_authn_start_initial_heap
#   Description: "ssm_authn_start_initial_heap"
#   default value: ssm_authn_start_initial_heap = "512m"

# variable: ssm_authn_start_max_heap
#   Description: "ssm_authn_start_max_heap"
#   default value: ssm_authn_start_max_heap = "2048m"

# variable: ssm_authn_start_newrelic_setting
#   Description: "ssm_authn_start_newrelic_setting"
#   default value: ssm_authn_start_newrelic_setting = "512m"

# variable: ssm_authn_start_service_port
#   Description: "ssm_authn_start_service_port"
#   default value: ssm_authn_start_service_port = "8443"

# variable: ssm_authn_start_webprotocol
#   Description: "ssm_authn_start_webprotocol"
#   default value: ssm_authn_start_webprotocol = "https"

# variable: ssm_authn_sumologic_enabled
#   Description: "ssm_authn_sumologic_enabled"
#   default value: ssm_authn_sumologic_enabled = "true"

# variable: ssm_authn_truststore_file
#   Description: "ssm_authn_truststore_file"
#   default value: ssm_authn_truststore_file = "file:conf/vosp-truststore.jks"

# variable: ssm_authn_truststore_password
#   Description: "ssm_authn_truststore_password"
ssm_authn_truststore_password = "changeit"

# variable: ssm_authn_vosp_session_timeout
#   Description: "ssm_authn_vosp_session_timeout"
#   default value: ssm_authn_vosp_session_timeout = "15"

# variable: ssm_authn_vosp_session_timeout_hard
#   Description: "ssm_authn_vosp_session_timeout_hard"
#   default value: ssm_authn_vosp_session_timeout_hard = "720"

# variable: ssm_naeserverip
#   Description: "ssm_naeserverip"
#   Defined in common.
#   default value: ssm_naeserverip = "*************:*************"
ssm_naeserverip = "*************:*************"

# variable: veracode_internal_cidr
#   Description: "CIDR block for internal private IPs"
#   default value: veracode_internal_cidr = "**********/16"
