ami_name_regex = "veracode-awslinux-authn-24.2.0"
ami_owners = "055143528572"

agora_vpc_cidr_block = "10.170.0.0/16"
ssm_app_kms_properties_aws_access_key = "********************"
ssm_app_kms_properties_aws_kpk_master_alias = "arn:aws:kms:us-east-1:227890167531:key/1389d78d-7e32-41f4-8b38-381336cf0b5e"
ssm_app_kms_properties_aws_region = "us-east-1"
ssm_app_kms_properties_aws_secret_access_key = "nqqtWGaBWXQkGQCrO0dznq52T7PoPHCC8IZGNWcO"
ssm_app_kms_properties_kpk_master_alias = "arn:aws:kms:us-east-1:227890167531:key/1389d78d-7e32-41f4-8b38-381336cf0b5e"
ssm_authn_ingriannae_keystore_password = "changeit"
ssm_authn_ingriannae_kmsipaddress = "*************:*************"
ssm_authn_keystore_password = "changeit"
ssm_authn_kms_aws_kpk_master_key_alias = "arn:aws:kms:us-east-1:227890167531:alias/veracode-nonproduction_vosp-master-key"
ssm_authn_kms_aws_region = "us-east-1"
ssm_authn_principalsigningpublickey = "veracode.io"
ssm_authn_truststore_password = "changeit"
ssm_naeserverip = "*************:*************"
authn_instance_type = "t3.medium"
asg_max = "0"
ssm_authn_redis_host = "agorstag132-identity.6vvrh2.ng.0001.use1.cache.amazonaws.com"
ssm_authn_redis_enabled = "true"  # set this to true after agora runbook executed
newrelic_enabled = "false"
