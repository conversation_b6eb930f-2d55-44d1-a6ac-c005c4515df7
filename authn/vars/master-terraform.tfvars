region = "us-east-1"
remote_state_bucket = "veracode-platform-nonprod-terraform-state"

ssm_authn_kms_default_kms = "AWSKMS"
ssm_authn_kms_aws_kpk_master_key_alias = "arn:aws:kms:us-east-1:227890167531:alias/veracode-nonproduction_vosp-master-key"
ssm_authn_kms_aws_region = "us-east-1"
ssm_authn_keystore_password = "changeit"
ssm_authn_truststore_password = "changeit"
ssm_authn_db_user = "appstg101"
ssm_authn_db_password = "2bb888f100140bb765dbab47b2c7a5b9"
ssm_authn_db_url = "************************************************************************************************************************************************** = stg01.ec2.internal)))"
ssm_authn_principalsigningpublickey =  "veracode.io"
ssm_authn_ingriannae_keystore_password = "changeit"
ssm_authn_ingriannae_kmsipaddress = "*************:*************"
ssm_app_kms_properties_aws_kpk_master_alias = "arn:aws:kms:us-east-1:227890167531:key/1389d78d-7e32-41f4-8b38-381336cf0b5e"
ssm_app_kms_properties_aws_secret_access_key = "changeit"
ssm_app_kms_properties_aws_region = "us-east-1"
ssm_app_kms_properties_kpk_master_alias =  "MasterKeyGarnetg"
ssm_app_kms_properties_aws_access_key = "changeit"
ssm_naeserverip = "*************:*************"
authn_instance_type = "t3.medium"

tags_application = "authn"
tags_application_long = "authn"
tags_cost_center = "CloudEng"
tags_domain = "stage.veracode.io"
tags_email = "<EMAIL>"
#tags_env = "stage-101"
tags_owner = "Debashis Das"
tags_product = "platform"
tags_purpose = "AWS-TEST-STACK"
tags_stack_name = "master"
tags_terraformManaged = "true"
