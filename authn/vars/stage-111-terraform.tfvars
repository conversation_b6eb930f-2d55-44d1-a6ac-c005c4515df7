ami_name_regex = "veracode-awslinux-authn-20.3.3"
ssm_app_kms_properties_aws_access_key = "********************"
ssm_app_kms_properties_aws_kpk_master_alias = "arn:aws:kms:us-east-1:227890167531:key/1389d78d-7e32-41f4-8b38-381336cf0b5e"
ssm_app_kms_properties_aws_region = "us-east-1"
ssm_app_kms_properties_aws_secret_access_key = "nqqtWGaBWXQkGQCrO0dznq52T7PoPHCC8IZGNWcO"
ssm_app_kms_properties_kpk_master_alias = "arn:aws:kms:us-east-1:227890167531:key/1389d78d-7e32-41f4-8b38-381336cf0b5e"
ssm_authn_ingriannae_keystore_password = "changeit"
ssm_authn_ingriannae_kmsipaddress = "*************:*************"
ssm_authn_keystore_password = "changeit"
ssm_authn_kms_aws_kpk_master_key_alias = "arn:aws:kms:us-east-1:227890167531:alias/veracode-nonproduction_vosp-master-key"
ssm_authn_kms_aws_region = "us-east-1"
ssm_authn_principalsigningpublickey = "veracode.io"
ssm_authn_truststore_password = "changeit"
ssm_naeserverip = "*************:*************"
agora_vpc_cidr_block = "**********/16"
