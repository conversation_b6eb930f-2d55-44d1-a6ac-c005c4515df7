ami_name_regex = "veracode-awslinux-authn-25.1.0"
agora_vpc_cidr_block = "**********/16"
ssm_app_kms_properties_aws_access_key = "changeit"
ssm_app_kms_properties_aws_kpk_master_alias = "arn:aws:kms:us-east-1:426703640137:key/06c0b6e7-e554-42ca-9721-f211add807e5"
ssm_app_kms_properties_aws_region = "us-east-1"
ssm_app_kms_properties_aws_secret_access_key = "changeit"
ssm_app_kms_properties_kpk_master_alias = "arn:aws:kms:us-east-1:426703640137:key/06c0b6e7-e554-42ca-9721-f211add807e5"
ssm_authn_ingriannae_keystore_password = "changeit"
ssm_authn_ingriannae_kmsipaddress = "*************:*************"
ssm_authn_keystore_password = "changeit"
ssm_authn_kms_aws_kpk_master_key_alias = "arn:aws:kms:us-east-1:426703640137:alias/veracode-production_vosp-master-key"
ssm_authn_kms_aws_region = "us-east-1"
ssm_authn_principalsigningpublickey = "veracode.io"
ssm_authn_truststore_password = "changeit"
ssm_naeserverip = "*************:*************"
authn_instance_type = "c5.xlarge"
ssm_authn_client_auth = "need"
asg_desired = 0
asg_max = 0
asg_min = 0
root_volume_size = "128"
ssm_authn_redis_enabled = "true"
ssm_authn_agora_security_principal_jwt_public_file = "conf/agora-identityservice-principal-signing-prod-1.pem"
ssm_authn_redis_host = "agorprod4-identity.nfpqr4.ng.0001.use1.cache.amazonaws.com"
health_check_type = "ELB"
ssm_authn_db_min_idle = 5
ssm_authn_db_init_size = 5

ssm_authn_kms_aws_s3_role_arn="arn:aws:iam::041513053014:role/vosp-S3-role-prod"

# APM configuration
newrelic_enabled = "false"
datadog_apm_enabled = "true"
datadog_apm_profiling_enabled = "false"
#datadog_apm_trace_methods = ""
