locals {
  tags = {
    env              = "${element(split("-", terraform.workspace), 0)}"
    stack_name       = "${terraform.workspace}"
    product          = "${data.terraform_remote_state.common.tags_product}"
    application      = "authn"
    cost_center      = "${data.terraform_remote_state.common.tags_cost_center}"
    domain           = "${data.terraform_remote_state.common.tags_domain}"
    email            = "${data.terraform_remote_state.common.tags_email}"
    owner            = "${data.terraform_remote_state.common.tags_owner}"
    region           = "${data.terraform_remote_state.common.tags_region}"
    terraformManaged = true
  }

  use_secrets_manager = "${local.tags["env"] == "prod" ? 1 : 0}"

  remote_state_bucket           = "veracode-platform-${element(split("-", terraform.workspace), 0) == "prod" ? "prod" : "nonprod"}-terraform-state"
  #ssm_authn_kms_aws_s3_role_arn = "arn:aws:iam::${local.tags["env"] == "prod" ? "************" : "************"}:role/vosp-S3-role-${terraform.workspace}"
  ssm_authn_kms_aws_s3_role_arn = "${var.ssm_authn_kms_aws_s3_role_arn != "" ? var.ssm_authn_kms_aws_s3_role_arn : "arn:aws:iam::${local.tags["env"] == "prod" ? "************" : "************"}:role/vosp-S3-role-${terraform.workspace}"}"
  r53_dns_publisher_role_arn    = "arn:aws:iam::************:role/dns-publisher-${local.tags["env"] == "prod" ? "prod" : "nonprod"}"
  r53_hosted_zone_id            = "${local.tags["env"] == "prod" ? "Z22QO6XH3U57YL" : "Z17G4HX0GKDHTG"}"

  # ??? Prod equivalent?? Ask Karthik.
  r53_service_account_profile = "terraform_gitlab_055143528572"
}
