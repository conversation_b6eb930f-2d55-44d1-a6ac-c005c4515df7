resource "aws_ssm_parameter" "SafeNetUser" {
  name      = "/${terraform.workspace}/SafeNetUser"
  type      = "String"
  value     = "${var.ssm_safenetuser}"
  overwrite = true
  tier      = "Advanced"
}

resource "aws_ssm_parameter" "SafeNetUserGroup" {
  name      = "/${terraform.workspace}/SafeNetUserGroup"
  type      = "String"
  value     = "${var.ssm_safenetusergroup}"
  overwrite = true
  tier      = "Advanced"
}

resource "aws_ssm_parameter" "SafeNetTestAlias" {
  name      = "/${terraform.workspace}/SafeNetTestAlias"
  type      = "String"
  value     = "${var.ssm_safenettestalias}"
  overwrite = true
  tier      = "Advanced"
}

resource "aws_ssm_parameter" "SafeNetTimeoutMillis" {
  name      = "/${terraform.workspace}/SafeNetTimeoutMillis"
  type      = "String"
  value     = "${var.ssm_safenettimeoutmillis}"
  overwrite = true
  tier      = "Advanced"
}

resource "aws_ssm_parameter" "NAEProtocol" {
  name      = "/${terraform.workspace}/NAEProtocol"
  type      = "String"
  value     = "${var.ssm_naeprotocol}"
  overwrite = true
  tier      = "Advanced"
}


resource "aws_ssm_parameter" "APP_KMS_PROPERTIES_aws_kpk_master_alias" {
  name      = "/${terraform.workspace}/APP_KMS_PROPERTIES_aws_kpk_master_alias"
  type      = "String"
  value     = "${var.ssm_app_kms_properties_aws_kpk_master_alias}"
  overwrite = true
  tier      = "Advanced"
}

resource "aws_ssm_parameter" "APP_KMS_PROPERTIES_aws_secret_access_key" {
  name      = "/${terraform.workspace}/APP_KMS_PROPERTIES_aws_secret_access_key"
  type      = "SecureString"
  key_id    = "${data.terraform_remote_state.common.ssm_kms_key}"
  value     = "${var.ssm_app_kms_properties_aws_secret_access_key}"
  overwrite = true
  tier      = "Advanced"

  lifecycle {
    ignore_changes = ["value"]
  }
}

resource "aws_ssm_parameter" "APP_KMS_PROPERTIES_aws_region" {
  name      = "/${terraform.workspace}/APP_KMS_PROPERTIES_aws_region"
  type      = "String"
  value     = "${var.ssm_app_kms_properties_aws_region}"
  overwrite = true
  tier      = "Advanced"
}

resource "aws_ssm_parameter" "APP_KMS_PROPERTIES_kpk_master_alias" {
  name      = "/${terraform.workspace}/APP_KMS_PROPERTIES_kpk_master_alias"
  type      = "String"
  value     = "${var.ssm_app_kms_properties_kpk_master_alias}"
  overwrite = true
  tier      = "Advanced"
}

resource "aws_ssm_parameter" "APP_KMS_PROPERTIES_aws_access_key" {
  name      = "/${terraform.workspace}/APP_KMS_PROPERTIES_aws_access_key"
  type      = "SecureString"
  key_id    = "${data.terraform_remote_state.common.ssm_kms_key}"
  value     = "${var.ssm_app_kms_properties_aws_access_key}"
  overwrite = true
  tier      = "Advanced"

  lifecycle {
    ignore_changes = ["value"]
  }
}

resource "aws_ssm_parameter" "NAEServerIP" {
  name      = "/${terraform.workspace}/NAEServerIP"
  type      = "String"
  value     = "${var.ssm_naeserverip}"
  overwrite = true
  tier      = "Advanced"
}

resource "aws_ssm_parameter" "ENVIRONMENT_INSTANCE_TYPE" {
  name      = "/${terraform.workspace}/ENVIRONMENT_INSTANCE_TYPE"
  type      = "String"
  value     = "CLOUD"
  overwrite = true
  tier      = "Advanced"
}
