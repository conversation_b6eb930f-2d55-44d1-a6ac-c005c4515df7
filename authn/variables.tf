variable "region" {
  description = "AWS region infrastructure lives in"
  default     = "us-east-1"
}

variable "ssm_safenetuser" {
  description = "ssm_safenetuser"
  default     = "jbossusr"
}

variable "ssm_safenetusergroup" {
  description = "ssm_safenetusergroup"
  default     = "key_users"
}

variable "ssm_safenettestalias" {
  description = "ssm_safenettestalias"
  default     = "test_key"
}

variable "ssm_safenettimeoutmillis" {
  description = "ssm_safenettimeoutmillis"
  default     = "3600000"
}

variable "ssm_naeprotocol" {
  description = "ssm_naeprotocol"
  default     = "ssl"
}

variable "ssm_app_kms_properties_aws_kpk_master_alias" {
  description = "ssm_app_kms_properties_aws_kpk_master_alias"
}

variable "ssm_app_kms_properties_aws_secret_access_key" {
  description = "ssm_app_kms_properties_aws_secret_access_key"
}

variable "ssm_app_kms_properties_aws_region" {
  description = "ssm_app_kms_properties_aws_region"
}

variable "ssm_app_kms_properties_kpk_master_alias" {
  description = "ssm_app_kms_properties_kpk_master_alias"
}

variable "ssm_app_kms_properties_aws_access_key" {
  description = "ssm_app_kms_properties_aws_access_key"
}

variable "ssm_naeserverip" {
  description = "ssm_naeserverip"
}

variable "ssm_authn_kms_aws_s3_role_arn" {
  description = "ssm_authn_kms_aws_s3_role_arn"
  default     = ""
}
